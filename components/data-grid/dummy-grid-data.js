/**
 * Dummy Grid Data Generator
 * Generates realistic sample data for testing the SnapGrid component
 * Following the pattern of components/charts/dummy-data.js
 * 
 * Features:
 * - Product listings with ASIN, title, sales, revenue, marketplace data
 * - Large dataset generation for virtual scrolling testing
 * - Various data types: strings, numbers, dates, booleans
 * - Realistic marketplace data with proper formatting
 * - Performance-optimized data generation
 * 
 * @version 1.0.0
 * <AUTHOR> Dashboard Team
 */

// Deterministic PRNG implementation (mulberry32)
let seededRandom = null;
let originalRandom = null;

/**
 * Mulberry32 deterministic PRNG
 * @param {number} seed - Seed value
 * @returns {Function} Seeded random function
 */
function mulberry32(seed) {
    return function() {
        seed = seed + 0x6D2B79F5 | 0;
        let t = Math.imul(seed ^ seed >>> 15, 1 | seed);
        t = t + Math.imul(t ^ t >>> 7, 61 | t) ^ t;
        return ((t ^ t >>> 14) >>> 0) / 4294967296;
    };
}

/**
 * Set seed for reproducible data generation
 * @param {number} seed - Seed value
 */
function setSeed(seed) {
    if (seed !== null && seed !== undefined) {
        if (!originalRandom) {
            originalRandom = Math.random;
        }
        seededRandom = mulberry32(seed);
        // Temporarily replace Math.random
        Math.random = seededRandom;
    }
}

/**
 * Reset Math.random to native behavior
 */
function resetRandom() {
    if (originalRandom) {
        Math.random = originalRandom;
        seededRandom = null;
        originalRandom = null;
    }
}

// Sample data pools for realistic generation
const SAMPLE_DATA = {
    marketplaces: [
        'US', 'UK', 'DE', 'FR', 'IT', 'ES', 'JP'
    ],
    
    categories: [
        'Standard t-shirt', 'Premium t-shirt', 'V-neck t-shirt', 'Tank top', 'Long sleeve t-shirt', 
        'Raglan', 'Sweatshirt', 'Pullover hoodie', 'Zip hoodie', 'PopSockets', 
        'iPhone cases', 'Samsung Galaxy cases', 'Tote bag', 'Throw pillows', 'Tumbler'
    ],
    
    designs: [
        'Vintage Logo', 'Minimalist Text', 'Abstract Art', 'Nature Scene', 'Geometric Pattern',
        'Typography Design', 'Retro Style', 'Modern Graphic', 'Artistic Print', 'Custom Quote',
        'Band Merch', 'Sports Theme', 'Holiday Special', 'Motivational', 'Funny Saying'
    ],
    
    colors: [
        'Black', 'White', 'Navy', 'Gray', 'Red', 'Blue', 'Green', 'Purple',
        'Orange', 'Yellow', 'Pink', 'Brown', 'Maroon', 'Teal', 'Olive'
    ],
    
    sizes: ['XS', 'S', 'M', 'L', 'XL', 'XXL', 'XXXL'],

    productTypes: [
        'Standard t-shirt', 'Premium t-shirt', 'V-neck t-shirt', 'Tank top', 'Long sleeve t-shirt', 
        'Raglan', 'Sweatshirt', 'Pullover hoodie', 'Zip hoodie', 'PopSockets', 
        'iPhone cases', 'Samsung Galaxy cases', 'Tote bag', 'Throw pillows', 'Tumbler'
    ],

    brands: [
        'Bella+Canvas', 'Gildan', 'Next Level', 'American Apparel', 'Hanes',
        'Fruit of the Loom', 'Champion', 'Alternative', 'Anvil', 'Port & Company'
    ],

    statuses: ['Draft', 'Translating', 'Under Review', 'Declined', 'Rejected', 'Processing', 'Timed out', 'Auto-uploaded', 'Live', 'Removed', 'Locked'],
    
    // Common product title patterns
    titlePatterns: [
        '{design} {category} - {color}',
        '{category} with {design}',
        'Premium {color} {category} - {design}',
        '{design} - Unisex {category}',
        'Custom {category} - {design} Print',
        '{color} {category} featuring {design}',
        'Vintage {design} {category}',
        'Modern {design} on {color} {category}'
    ]
};

/**
 * Generate a random ASIN (Amazon Standard Identification Number)
 */
function generateASIN() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let asin = '';
    for (let i = 0; i < 10; i++) {
        asin += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return asin;
}

/**
 * Generate a realistic product title
 */
function generateProductTitle() {
    const pattern = SAMPLE_DATA.titlePatterns[Math.floor(Math.random() * SAMPLE_DATA.titlePatterns.length)];
    const design = SAMPLE_DATA.designs[Math.floor(Math.random() * SAMPLE_DATA.designs.length)];
    const category = SAMPLE_DATA.categories[Math.floor(Math.random() * SAMPLE_DATA.categories.length)];
    const color = SAMPLE_DATA.colors[Math.floor(Math.random() * SAMPLE_DATA.colors.length)];
    
    return pattern
        .replace('{design}', design)
        .replace('{category}', category)
        .replace('{color}', color);
}

/**
 * Generate random sales data with realistic distribution
 */
function generateSalesData() {
    // Use weighted random for more realistic sales distribution
    const weights = [0.4, 0.3, 0.2, 0.1]; // Most products have low sales
    const ranges = [
        [0, 10],      // 40% have 0-10 sales
        [11, 50],     // 30% have 11-50 sales
        [51, 200],    // 20% have 51-200 sales
        [201, 1000]   // 10% have 201-1000 sales
    ];
    
    const random = Math.random();
    let cumulative = 0;
    
    for (let i = 0; i < weights.length; i++) {
        cumulative += weights[i];
        if (random <= cumulative) {
            const [min, max] = ranges[i];
            return Math.floor(Math.random() * (max - min + 1)) + min;
        }
    }
    
    return Math.floor(Math.random() * 10); // Fallback
}

/**
 * Generate revenue based on sales with realistic pricing
 */
function generateRevenue(sales) {
    const basePrice = 15 + Math.random() * 25; // $15-40 base price
    const royaltyRate = 0.1 + Math.random() * 0.15; // 10-25% royalty
    return Math.round(sales * basePrice * royaltyRate * 100) / 100;
}

/**
 * Generate a random date within the last year
 */
function generateRandomDate() {
    const now = new Date();
    const yearAgo = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate());
    const randomTime = yearAgo.getTime() + Math.random() * (now.getTime() - yearAgo.getTime());
    return new Date(randomTime);
}

/**
 * Generate a random BSR (Best Seller Rank)
 */
function generateBSR() {
    // BSR ranges from 1 to millions, with most products having higher numbers
    const weights = [0.05, 0.15, 0.3, 0.5]; // Distribution weights
    const ranges = [
        [1, 1000],        // 5% have very good BSR (1-1000)
        [1001, 10000],    // 15% have good BSR (1001-10000)
        [10001, 100000],  // 30% have average BSR (10001-100000)
        [100001, 1000000] // 50% have poor BSR (100001-1000000)
    ];

    const random = Math.random();
    let cumulative = 0;

    for (let i = 0; i < weights.length; i++) {
        cumulative += weights[i];
        if (random <= cumulative) {
            const [min, max] = ranges[i];
            return Math.floor(Math.random() * (max - min + 1)) + min;
        }
    }

    return Math.floor(Math.random() * 1000000) + 1; // Fallback
}

/**
 * Generate a design ID
 */
function generateDesignID() {
    const prefix = 'DES';
    const number = Math.floor(Math.random() * 999999) + 1;
    return `${prefix}${number.toString().padStart(6, '0')}`;
}

/**
 * Generate returns based on sales (typically 1-5% return rate)
 */
function generateReturns(sales) {
    const returnRate = Math.random() * 0.04 + 0.01; // 1-5% return rate
    return Math.floor(sales * returnRate);
}

/**
 * Generate a single product record
 */
function generateProductRecord(index = 0) {
    const sales = generateSalesData();
    const revenue = generateRevenue(sales);
    const marketplace = SAMPLE_DATA.marketplaces[Math.floor(Math.random() * SAMPLE_DATA.marketplaces.length)];
    const status = SAMPLE_DATA.statuses[Math.floor(Math.random() * SAMPLE_DATA.statuses.length)];
    const productType = SAMPLE_DATA.productTypes[Math.floor(Math.random() * SAMPLE_DATA.productTypes.length)];
    const brand = SAMPLE_DATA.brands[Math.floor(Math.random() * SAMPLE_DATA.brands.length)];
    const returns = generateReturns(sales);
    const returnRate = sales > 0 ? Math.round((returns / sales) * 100 * 100) / 100 : 0; // Calculate return rate as numeric percentage
    const price = Math.round((15 + Math.random() * 25) * 100) / 100; // $15-40 price
    const royalties = Math.round(sales * price * (0.1 + Math.random() * 0.15) * 100) / 100; // 10-25% royalty
    const firstSold = generateRandomDate();
    const lastSold = new Date(firstSold.getTime() + Math.random() * (Date.now() - firstSold.getTime()));
    const firstPublished = new Date(firstSold.getTime() - Math.random() * 30 * 24 * 60 * 60 * 1000); // Published before first sale

    return {
        id: index + 1,
        marketplace: marketplace,
        asin: generateASIN(),
        productType: productType,
        status: status,
        brand: brand,
        title: generateProductTitle(),
        price: price,
        sales: sales,
        returns: returns,
        returnRate: returnRate,
        royalties: royalties,
        firstSold: firstSold,
        lastSold: lastSold,
        bsr: generateBSR(),
        firstPublished: firstPublished,
        lastUpdated: generateRandomDate(),
        reviews: Math.floor(Math.random() * 500),
        designId: generateDesignID(),

        // Keep existing fields for backward compatibility
        revenue: revenue,
        active: Math.random() > 0.2, // 80% active
        category: SAMPLE_DATA.categories[Math.floor(Math.random() * SAMPLE_DATA.categories.length)],
        color: SAMPLE_DATA.colors[Math.floor(Math.random() * SAMPLE_DATA.colors.length)],
        size: SAMPLE_DATA.sizes[Math.floor(Math.random() * SAMPLE_DATA.sizes.length)],
        createdDate: generateRandomDate(),
        views: Math.floor(Math.random() * 10000),
        clicks: Math.floor(Math.random() * 1000),
        conversion: Math.round((Math.random() * 0.1 + 0.01) * 10000) / 100, // 1-11% conversion rate
        rating: Math.round((Math.random() * 2 + 3) * 10) / 10, // 3.0-5.0 rating
        inventory: Math.floor(Math.random() * 100),
        cost: Math.round((5 + Math.random() * 15) * 100) / 100, // $5-20 cost
        profit: Math.round((revenue - (5 + Math.random() * 15)) * 100) / 100
    };
}

/**
 * Generate an array of product data
 * @param {number} count - Number of records to generate
 * @param {Object} options - Generation options
 * @returns {Array} Array of product records
 */
function generateProductData(count = 100, options = {}) {
    const {
        includeNulls = true,
        includeSpecialChars = true,
        includeLongText = true,
        seed = null,
        batchSize = 1000 // Process in batches for large datasets
    } = options;
    
    // Set seed for reproducible data if provided
    if (seed !== null) {
        setSeed(seed);
    }
    
    const data = [];
    const startTime = performance.now();
    
    // For large datasets, use batch processing
    if (count > 10000) {
        return generateProductDataBatched(count, options);
    }
    
    for (let i = 0; i < count; i++) {
        let record = generateProductRecord(i);
        
        // Add some null values for testing
        if (includeNulls && Math.random() < 0.05) {
            const nullFields = ['rating', 'reviews', 'inventory'];
            const fieldToNull = nullFields[Math.floor(Math.random() * nullFields.length)];
            record[fieldToNull] = null;
        }
        
        // Add special characters for testing
        if (includeSpecialChars && Math.random() < 0.1) {
            record.title += ' ™ & © Special "Chars" <test>';
        }
        
        // Add very long text for testing
        if (includeLongText && Math.random() < 0.05) {
            record.title = record.title + ' - ' + 'Very long description that should test text overflow and ellipsis behavior in the grid cells when the content exceeds the available width';
        }
        
        data.push(record);
    }
    
    const endTime = performance.now();
    console.log(`Generated ${count} records in ${(endTime - startTime).toFixed(1)}ms`);
    
    // Reset Math.random if we used seeding
    if (seed !== null) {
        resetRandom();
    }
    
    return data;
}

/**
 * Generate large datasets using batch processing for better memory management
 * @param {number} count - Number of records to generate
 * @param {Object} options - Generation options
 * @returns {Array} Array of product records
 */
function generateProductDataBatched(count = 100000, options = {}) {
    const {
        includeNulls = true,
        includeSpecialChars = true,
        includeLongText = true,
        seed = null,
        batchSize = 1000,
        onProgress = null
    } = options;
    
    // Set seed for reproducible data if provided
    if (seed !== null) {
        setSeed(seed);
    }
    
    const data = [];
    const startTime = performance.now();
    const totalBatches = Math.ceil(count / batchSize);
    
    console.log(`Generating ${count} records in ${totalBatches} batches of ${batchSize}...`);
    
    for (let batch = 0; batch < totalBatches; batch++) {
        const batchStart = batch * batchSize;
        const batchEnd = Math.min(batchStart + batchSize, count);
        
        // Generate batch
        for (let i = batchStart; i < batchEnd; i++) {
            let record = generateProductRecord(i);
            
            // Add some null values for testing
            if (includeNulls && Math.random() < 0.05) {
                const nullFields = ['rating', 'reviews', 'inventory'];
                const fieldToNull = nullFields[Math.floor(Math.random() * nullFields.length)];
                record[fieldToNull] = null;
            }
            
            // Add special characters for testing
            if (includeSpecialChars && Math.random() < 0.1) {
                record.title += ' ™ & © Special "Chars" <test>';
            }
            
            // Add very long text for testing
            if (includeLongText && Math.random() < 0.05) {
                record.title = record.title + ' - ' + 'Very long description that should test text overflow and ellipsis behavior in the grid cells when the content exceeds the available width';
            }
            
            data.push(record);
        }
        
        // Report progress
        const progress = Math.round(((batch + 1) / totalBatches) * 100);
        if (onProgress) {
            onProgress(progress, batch + 1, totalBatches);
        }
    }
    
    const endTime = performance.now();
    console.log(`Generated ${count} records in ${(endTime - startTime).toFixed(1)}ms (${(count / ((endTime - startTime) / 1000)).toFixed(0)} records/sec)`);
    
    // Reset Math.random if we used seeding
    if (seed !== null) {
        resetRandom();
    }
    
    return data;
}

/**
 * Generate 100K rows with progress callback
 * @param {Function} onProgress - Progress callback function
 * @returns {Array} Array of 100,000 product records
 */
function generate100KProductData(onProgress = null) {
    return generateProductDataBatched(100000, {
        includeNulls: true,
        includeSpecialChars: true,
        includeLongText: true,
        seed: 12345, // Fixed seed for reproducible data
        batchSize: 1000,
        onProgress: onProgress
    });
}

/**
 * Generate hierarchical data for grouping tests
 * @param {number} count - Number of records to generate
 * @returns {Array} Array of hierarchical records
 */
function generateHierarchicalData(count = 100) {
    const data = generateProductData(count);
    
    // Add hierarchy fields
    return data.map(record => ({
        ...record,
        region: record.marketplace.includes('Amazon') ? 
            record.marketplace.split(' ')[1] || 'US' : 'Other',
        performance: record.sales > 100 ? 'High' : 
                    record.sales > 50 ? 'Medium' : 'Low',
        priceRange: record.revenue > 100 ? 'Premium' :
                   record.revenue > 50 ? 'Standard' : 'Budget'
    }));
}

/**
 * Generate time series data for analytics
 * @param {number} days - Number of days to generate
 * @returns {Array} Array of daily analytics records
 */
function generateTimeSeriesData(days = 30) {
    const data = [];
    const today = new Date();
    
    for (let i = 0; i < days; i++) {
        const date = new Date(today);
        date.setDate(date.getDate() - i);
        
        data.push({
            date: date,
            sales: Math.floor(Math.random() * 100),
            revenue: Math.round((Math.random() * 1000) * 100) / 100,
            orders: Math.floor(Math.random() * 50),
            visitors: Math.floor(Math.random() * 1000),
            conversion: Math.round((Math.random() * 0.1) * 10000) / 100
        });
    }
    
    return data.reverse(); // Oldest first
}

/**
 * Generate performance test data with specific characteristics
 * @param {number} count - Number of records
 * @param {string} type - Type of test data ('memory', 'scroll', 'filter', 'sort')
 * @returns {Array} Optimized test data
 */
function generatePerformanceTestData(count = 10000, type = 'scroll') {
    console.log(`Generating ${count} records for ${type} performance testing...`);
    const startTime = performance.now();
    
    const data = [];
    
    for (let i = 0; i < count; i++) {
        let record;
        
        switch (type) {
            case 'memory':
                // Minimal data for memory testing
                record = {
                    id: i,
                    asin: generateASIN(),
                    sales: Math.floor(Math.random() * 1000),
                    revenue: Math.round(Math.random() * 1000 * 100) / 100
                };
                break;
                
            case 'filter':
                // Data optimized for filter testing
                record = generateProductRecord(i);
                // Ensure good distribution for filtering
                record.marketplace = SAMPLE_DATA.marketplaces[i % SAMPLE_DATA.marketplaces.length];
                record.status = SAMPLE_DATA.statuses[i % SAMPLE_DATA.statuses.length];
                break;
                
            case 'sort':
                // Data with varied sort values
                record = generateProductRecord(i);
                record.sales = i % 1000; // Predictable sort order
                break;
                
            default:
                record = generateProductRecord(i);
        }
        
        data.push(record);
    }
    
    const endTime = performance.now();
    console.log(`Generated ${count} ${type} test records in ${(endTime - startTime).toFixed(1)}ms`);
    
    return data;
}

// Export functions for use in tests
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        generateProductData,
        generateProductDataBatched,
        generate100KProductData,
        generateHierarchicalData,
        generateTimeSeriesData,
        generatePerformanceTestData,
        generateProductRecord,
        setSeed,
        resetRandom,
        SAMPLE_DATA
    };
} else if (typeof window !== 'undefined') {
    window.generateProductData = generateProductData;
    window.generateProductDataBatched = generateProductDataBatched;
    window.generate100KProductData = generate100KProductData;
    window.generateHierarchicalData = generateHierarchicalData;
    window.generateTimeSeriesData = generateTimeSeriesData;
    window.generatePerformanceTestData = generatePerformanceTestData;
    window.generateProductRecord = generateProductRecord;
    window.setSeed = setSeed;
    window.resetRandom = resetRandom;
    window.SAMPLE_DATA = SAMPLE_DATA;
}
