<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Snap Grid Header/Footer Test</title>
    <link rel="stylesheet" href="snap-grid.css">
    <style>
        body {
            margin: 20px;
            font-family: 'Amazon Ember', Arial, sans-serif;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .test-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 20px;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">Snap Grid with Header and Footer Test</h1>
        <div style="margin-bottom: 20px;">
            <button id="start-loading-btn" style="padding: 10px 20px; margin-right: 10px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">Start Loading Simulation</button>
            <button id="stop-loading-btn" style="padding: 10px 20px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer;">Stop Loading Simulation</button>
        </div>
        <div id="snap-grid-container" style="height: 600px;"></div>
    </div>

    <script src="snap-grid.js"></script>
    <script>
        // Sample data for testing - start with only 10 rows
        const sampleData = [];
        const marketplaces = ['US', 'UK', 'DE', 'FR', 'IT', 'ES', 'JP'];
        const statuses = ['Draft', 'Translating', 'Under Review', 'Declined', 'Rejected', 'Processing', 'Timed out', 'Auto-uploaded', 'Live', 'Removed', 'Locked'];
        const productTypes = ['Standard t-shirt', 'Premium t-shirt', 'V-neck t-shirt', 'Tank top', 'Long sleeve t-shirt', 'Raglan', 'Sweatshirt', 'Pullover hoodie', 'Zip hoodie', 'PopSockets', 'iPhone cases', 'Samsung Galaxy cases', 'Tote bag', 'Throw pillows', 'Tumbler'];
        
        // Generate initial 10 rows
        for (let i = 1; i <= 10; i++) {
            sampleData.push({
                id: i,
                preview: '',
                marketplace: marketplaces[Math.floor(Math.random() * marketplaces.length)],
                status: statuses[Math.floor(Math.random() * statuses.length)],
                productType: productTypes[Math.floor(Math.random() * productTypes.length)],
                title: `Product ${i}`,
                price: `$${(Math.random() * 50 + 10).toFixed(2)}`,
                sales: Math.floor(Math.random() * 1000),
                returns: Math.floor(Math.random() * 50),
                returnRate: (Math.random() * 10).toFixed(2) + '%',
                royalties: '$' + (Math.random() * 2000).toFixed(2),
                firstSold: 'Aug 31, 2025',
                lastSold: 'Sep 5, 2025',
                bsr: Math.floor(Math.random() * 1000000),
                firstPublished: 'Aug 24, 2025',
                actions: ''
            });
        }

        // Column configuration
        const columns = [
            { field: 'checkbox', headerName: '', width: 50, pinned: 'left', type: 'checkbox' },
            { field: 'preview', headerName: 'Preview', width: 80, pinned: 'left', type: 'preview' },
            { field: 'marketplace', headerName: 'Marketplace', width: 120, type: 'select' },
            { field: 'status', headerName: 'Status', width: 140, type: 'select' },
            { field: 'productType', headerName: 'Product Type', width: 160, type: 'select' },
            { field: 'title', headerName: 'Title', width: 200, type: 'text' },
            { field: 'price', headerName: 'Price', width: 100, type: 'currency' },
            { field: 'sales', headerName: 'Sales', width: 100, type: 'numeric' },
            { field: 'returns', headerName: 'Returns', width: 100, type: 'numeric' },
            { field: 'returnRate', headerName: 'Return Rate', width: 120, type: 'percentage' },
            { field: 'royalties', headerName: 'Royalties', width: 120, type: 'currency' },
            { field: 'firstSold', headerName: 'First Sold', width: 120, type: 'date' },
            { field: 'lastSold', headerName: 'Last Sold', width: 120, type: 'date', sortable: true },
            { field: 'bsr', headerName: 'BSR', width: 120, type: 'numeric' },
            { field: 'firstPublished', headerName: 'First Published Date', width: 150, type: 'date' },
            { field: 'actions', headerName: 'Actions', width: 96, pinned: 'right', type: 'actions' }
        ];

        // Initialize the grid
        const grid = new SnapGrid({
            container: document.getElementById('snap-grid-container'),
            columns: columns,
            data: sampleData,
            height: '100%',
            virtualScrolling: true,
            rowSelection: true,
            theme: 'light'
        });

        // Set initial total products count
        grid.state.totalProducts = 1000;

        // Test the header and footer functionality
        console.log('Grid initialized with header and footer');
        console.log('Controls header:', grid.elements.controlsHeader);
        console.log('Delete button:', grid.elements.deleteBtn);
        console.log('Export button:', grid.elements.exportBtn);
        console.log('Footer:', grid.elements.footer);
        console.log('Selection info:', grid.elements.selectionInfo);
        console.log('Footer stats:', grid.elements.footerStats);

        // Add button event listeners for loading simulation
        document.getElementById('start-loading-btn').addEventListener('click', () => {
            console.log('Starting loading simulation...');
            grid.startLoadingSimulation();
        });

        document.getElementById('stop-loading-btn').addEventListener('click', () => {
            console.log('Stopping loading simulation...');
            grid.stopLoadingSimulation();
        });
    </script>
</body>
</html>